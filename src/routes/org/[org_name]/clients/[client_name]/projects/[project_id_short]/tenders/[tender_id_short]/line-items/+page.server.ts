import type { Actions, PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import { error, fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { projectUUID, tenderUUID } from '$lib/schemas/project';
import { createLineItemSchema } from '$lib/schemas/tender';
import { getTenderById, createLineItem, updateLineItem, deleteLineItem } from '$lib/tender_utils';
import { message } from 'sveltekit-superforms';

export const load: PageServerLoad = async ({ locals, params }) => {
	await requireUser();
	const { supabase } = locals;
	const { project_id_short } = requireProject();
	const { tender_id_short } = params;

	if (!tender_id_short) {
		throw error(400, 'Tender ID is required');
	}

	const project_id = projectUUID(project_id_short);
	const tender_id = tenderUUID(tender_id_short);

	// Fetch tender details with line items
	const tender = await getTenderById(supabase, tender_id);

	// Verify the tender belongs to the project
	if (tender.project_id !== project_id) {
		throw error(404, 'Tender not found');
	}

	// Initialize form for creating line items
	const form = await superValidate(zod(createLineItemSchema));

	return {
		tender,
		tender_id_short,
		form,
	};
};

export const actions: Actions = {
	create: async ({ request, locals, cookies, params }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_id_short } = requireProject();
		const { tender_id_short } = params;

		if (!tender_id_short) {
			throw error(400, 'Tender ID is required');
		}

		const tender_id = tenderUUID(tender_id_short);

		// Validate form data with superforms
		const form = await superValidate(request, zod(createLineItemSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			// Get current revision
			const { data: currentRevision, error: revisionError } = await supabase
				.from('tender_revision')
				.select('tender_revision_id')
				.eq('tender_id', tender_id)
				.eq('is_current', true)
				.single();

			if (revisionError || !currentRevision) {
				return fail(500, {
					form,
					message: { type: 'error', text: 'Failed to find current tender revision' },
				});
			}

			const lineItemData = {
				tender_revision_id: currentRevision.tender_revision_id,
				...form.data,
			};

			await createLineItem(supabase, lineItemData);
		} catch (err) {
			if (err instanceof Response) {
				throw err;
			}
			locals.log.error({ msg: 'Error creating line item:', err });
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create line item' },
			});
		}
		return message(form, {
			type: 'success',
			text: `Line item has been added successfully.`,
		});
	},

	update: async ({ request, locals, cookies, params }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_id_short } = requireProject();
		const { tender_id_short } = params;

		if (!tender_id_short) {
			throw error(400, 'Tender ID is required');
		}

		const formData = await request.formData();
		const lineItemId = formData.get('line_item_id') as string;

		if (!lineItemId) {
			return fail(400, {
				message: { type: 'error', text: 'Line item ID is required' },
			});
		}

		const lineItemData = {
			line_number: parseInt(formData.get('line_number') as string),
			description: formData.get('description') as string,
			quantity: formData.get('quantity') ? parseFloat(formData.get('quantity') as string) : null,
			unit: (formData.get('unit') as string) || null,
			material_rate: formData.get('material_rate')
				? parseFloat(formData.get('material_rate') as string)
				: null,
			labor_rate: formData.get('labor_rate')
				? parseFloat(formData.get('labor_rate') as string)
				: null,
			productivity_factor: formData.get('productivity')
				? parseFloat(formData.get('productivity') as string)
				: null,
			unit_rate: formData.get('unit_rate') ? parseFloat(formData.get('unit_rate') as string) : null,
			subtotal: formData.get('subtotal') ? parseFloat(formData.get('subtotal') as string) : null,
			normalization_type: 'amount' as const,
			normalization_amount: null,
			normalization_percentage: null,
			notes: (formData.get('notes') as string) || null,
		};

		try {
			await updateLineItem(supabase, lineItemId, lineItemData);

			throw redirect(
				302,
				`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
					client_name,
				)}/projects/${encodeURIComponent(project_id_short)}/tenders/${encodeURIComponent(
					tender_id_short,
				)}/line-items`,
				{
					type: 'success',
					message: 'Line item has been updated successfully.',
				},
				cookies,
			);
		} catch (err) {
			if (err instanceof Response) {
				throw err;
			}
			locals.log.error({ msg: 'Error updating line item:', err });
			return fail(500, {
				message: { type: 'error', text: 'Failed to update line item' },
			});
		}
	},

	delete: async ({ request, locals, cookies, params }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_id_short } = requireProject();
		const { tender_id_short } = params;

		if (!tender_id_short) {
			throw error(400, 'Tender ID is required');
		}

		const formData = await request.formData();
		const lineItemId = formData.get('line_item_id') as string;

		if (!lineItemId) {
			return fail(400, {
				message: { type: 'error', text: 'Line item ID is required' },
			});
		}

		try {
			await deleteLineItem(supabase, lineItemId);

			throw redirect(
				302,
				`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
					client_name,
				)}/projects/${encodeURIComponent(project_id_short)}/tenders/${encodeURIComponent(
					tender_id_short,
				)}/line-items`,
				{
					type: 'success',
					message: 'Line item has been deleted successfully.',
				},
				cookies,
			);
		} catch (err) {
			if (err instanceof Response) {
				throw err;
			}
			locals.log.error({ msg: 'Error deleting line item:', err });
			return fail(500, {
				message: { type: 'error', text: 'Failed to delete line item' },
			});
		}
	},
};
